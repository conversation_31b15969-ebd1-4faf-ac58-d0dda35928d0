import os
import time
import fcntl
import select
import subprocess


def get_bios_info():

    print("=== 测试SOL连接和输出读取 ===")

    outputs = []
    stderr_outputs = []

    cmd = ['ipmitool', '-I', 'lanplus', '-H', '************',
           '-U', 'Administrator', '-P', 'Superuser9!',
           'sol', 'activate'
            ]

    print("启动SOL进程...")
    sol_process = subprocess.Popen(
        cmd,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1,
        universal_newlines=True
    )

    print("等待进程启动...")
    time.sleep(3)
    
    # 检查进程状态
    if sol_process.poll() is not None:
        stdout, stderr = sol_process.communicate()
        print(f"进程已退出，返回码: {sol_process.returncode}")
        print(f"stdout: {stdout}")
        print(f"stderr: {stderr}")
    else:
        print("进程正在运行，尝试读取输出...")
        
        start_time = time.time()
        timeout = 10  # 10秒超时
        no_data_count = 0  # 连续无数据计数
        max_no_data = 50   # 最多等待5秒

        print("读取初始输出...")
        time.sleep(1)
        # print("发送上箭头按键...")
        # sol_process.stdin.write('\x1b[A')
        # print("发送下箭头按键...")
        # sol_process.stdin.write('\x1b[B')
        print("发送右箭头按键...")
        sol_process.stdin.write('\x1b[C')
        sol_process.stdin.flush()
        time.sleep(0.5)  # 给BMC反应时间
        # print("发送左箭头按键...")
        # sol_process.stdin.write('\x1b[D')
        # sol_process.stdin.flush()
        # time.sleep(0.5)  # 给BMC反应时间

        fd_out = sol_process.stdout.fileno()
        fd_err = sol_process.stderr.fileno()
        # 设置非阻塞
        fl_out = fcntl.fcntl(fd_out, fcntl.F_GETFL)
        fcntl.fcntl(fd_out, fcntl.F_SETFL, fl_out | os.O_NONBLOCK)
        fl_err = fcntl.fcntl(fd_err, fcntl.F_GETFL)
        fcntl.fcntl(fd_err, fcntl.F_SETFL, fl_err | os.O_NONBLOCK)

        while time.time() - start_time < timeout:
            if sol_process.poll() is not None:
                print("进程已退出")
                break

            ready, _, _ = select.select([sol_process.stdout, sol_process.stderr], [], [], 0.1)
            if not ready:
                no_data_count += 1
                if no_data_count > max_no_data:
                    break
                continue

            no_data_count = 0
            for stream in ready:
                try:
                    fd = stream.fileno()
                    data = os.read(fd, 1024)
                    if data:
                        if stream == sol_process.stdout:
                            outputs.append(data)
                        elif stream == sol_process.stderr:
                            stderr_outputs.append(data)
                except Exception as e:
                    import traceback
                    traceback.print_exc()
                    print(f"读取流时出错: {e}")
                    break

    # if outputs:
    #     print("STDOUT输出内容:")
    #     for i, output in enumerate(outputs):
    #         print(f"  {i+1}: {output}")
    
    # if stderr_outputs:
    #     print("STDERR输出内容:")
    #     for i, output in enumerate(stderr_outputs):
    #         print(f"  {i+1}: {output}")
    return outputs
