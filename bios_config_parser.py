#!/usr/bin/env python3
"""
BIOS配置项解析器
根据menus.yaml文件的格式和读取的BIOS页面信息，获取指定配置的配置项信息
"""

import re
import yaml
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass


@dataclass
class ConfigField:
    """配置字段数据类"""
    name: str
    value: str
    field_type: str  # 'readonly' or 'editable'
    options: List[str] = None
    keys: List[str] = None


@dataclass
class MenuConfig:
    """菜单配置数据类"""
    name: str
    path: str
    readonly_fields: List[Dict]
    editable_fields: List[Dict]
    submenus: List[Dict]


class BIOSConfigParser:
    """BIOS配置解析器"""

    def __init__(self, menus_yaml_path: str = "menus.yaml"):
        self.menus_config = self._load_menus_config(menus_yaml_path)
        self.ansi_escape = re.compile(r'\x1b\[[0-9;]*[mH]|\x1b\[[0-9]+;[0-9]+H')

    def _load_menus_config(self, yaml_path: str) -> Dict[str, MenuConfig]:
        """加载menus.yaml配置文件"""
        try:
            with open(yaml_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            menus_config = {}
            for menu_data in data.get('menus', []):
                menu_config = MenuConfig(
                    name=menu_data['name'],
                    path=menu_data['path'],
                    readonly_fields=menu_data.get('readOnlyFields', []),
                    editable_fields=menu_data.get('editableFields', []),
                    submenus=menu_data.get('subMenus', [])
                )
                menus_config[menu_config.name] = menu_config
            
            return menus_config
        except Exception as e:
            print(f"加载menus.yaml配置文件失败: {e}")
            return {}

    def clean_ansi_content(self, content: str) -> str:
        """清理ANSI转义字符"""
        if not content:
            return ""
        clean_content = self.ansi_escape.sub('', content)
        return clean_content.strip()

